group 'map.galliexpress.galli_vector_plugin'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.3'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'

android {

    compileSdkVersion 31
    ndkVersion "20.1.5948944"

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    dependencies {
        implementation 'org.maplibre.gl:android-sdk:10.2.0'
        implementation 'org.maplibre.gl:android-plugin-annotation-v9:2.0.0'
        implementation 'org.maplibre.gl:android-plugin-offline-v9:2.0.0'
        implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    }
    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.10.1'
}
java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(8)) // or 17 if required
    }
}
